import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Separator } from '@/components/ui/separator';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { 
    BookOpen, 
    ClipboardCheck, 
    FileCheck, 
    FileText, 
    LayoutGrid, 
    ShieldCheck, 
    UserCheck, 
    Users, 
    BarChart, 
    CalendarDays, 
    CheckCheck, 
    Settings, 
    Database, 
    ServerCog,
    CreditCard,
    Box
} from 'lucide-react';
import AppLogo from './app-logo';

interface PageProps {
    auth?: {
        user?: {
            role: string;
        };
    };
}

// Applicant navigation items
const applicantNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/applicant/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Residency Verification',
        href: '/applicant/residency-verification',
        icon: UserCheck,
    },
    {
        title: 'BFACES Application',
        href: '/applicant/bfaces-application',
        icon: FileText,
    },
    {
        title: 'Services',
        href: '/applicant/services',
        icon: ClipboardCheck,
    },
    {
        title: 'Applications',
        href: '/applicant/applications',
        icon: FileCheck,
    },
    {
        title: 'Appointments',
        href: '/applicant/appointments',
        icon: CalendarDays,
    },
    {
        title: 'Documents',
        href: '/applicant/documents',
        icon: BookOpen,
    },
];

// Social Worker navigation items
const socialWorkerNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/social-worker/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Verifications',
        href: '/social-worker/verifications',
        icon: CheckCheck,
    },
    {
        title: 'Applications',
        href: '/social-worker/applications',
        icon: FileCheck,
    },
    {
        title: 'Interviews',
        href: '/social-worker/interviews',
        icon: CalendarDays,
    },
    {
        title: 'Clients',
        href: '/social-worker/clients',
        icon: Users,
    },
    {
        title: 'Reports',
        href: '/social-worker/reports',
        icon: BarChart,
    },
];

// MSWDO Officer navigation items
const mswdoOfficerNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/mswdo-officer/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'System Configuration',
        href: '/mswdo-officer/configuration',
        icon: Settings,
    },
    {
        title: 'Service Management',
        href: '/mswdo-officer/services',
        icon: Box,
    },
    {
        title: 'User Management',
        href: '/mswdo-officer/users',
        icon: Users,
    },
    {
        title: 'Budget Allocation',
        href: '/mswdo-officer/budget',
        icon: CreditCard,
    },
    {
        title: 'Database Management',
        href: '/mswdo-officer/database',
        icon: Database,
    },
    {
        title: 'Reports & Analytics',
        href: '/mswdo-officer/reports',
        icon: BarChart,
    },
];

const footerNavItems: NavItem[] = [
    
];

export function AppSidebar() {
    const { auth } = usePage().props as PageProps;
    const userRole = auth?.user?.role || 'applicant';

    const getNavItems = () => {
        switch (userRole) {
            case 'social-worker':
                return socialWorkerNavItems;
            case 'mswdo-officer':
            case 'superadmin':
                return mswdoOfficerNavItems;
            default:
                return applicantNavItems;
        }
    };

    const getDashboardRoute = () => {
        switch (userRole) {
            case 'mswdo-officer':
            case 'superadmin':
                return '/mswdo-officer/dashboard';
            case 'social-worker':
                return '/social-worker/dashboard';
            default:
                return '/applicant/dashboard';
        }
    };

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href={getDashboardRoute()} prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={getNavItems()} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
