import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Separator } from '@/components/ui/separator';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { 
    BookOpen, 
    ClipboardCheck, 
    FileCheck, 
    FileText, 
    LayoutGrid, 
    ShieldCheck, 
    UserCheck, 
    Users, 
    BarChart, 
    CalendarDays, 
    CheckCheck, 
    Settings, 
    Database, 
    ServerCog,
    CreditCard,
    Box
} from 'lucide-react';
import AppLogo from './app-logo';

interface PageProps {
    auth?: {
        user?: {
            role: string;
        };
    };
}

// Applicant navigation items
const applicantNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/applicant/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Residency Verification',
        href: '/applicant/residency-verification',
        icon: UserCheck,
    },
    {
        title: 'BFACES Application',
        href: '/applicant/bfaces-application',
        icon: FileText,
    },
    {
        title: 'Services',
        href: '/applicant/services',
        icon: ClipboardCheck,
    },
    {
        title: 'Applications',
        href: '/applicant/applications',
        icon: FileCheck,
    },
    {
        title: 'Appointments',
        href: '/applicant/appointments',
        icon: CalendarDays,
    },
    {
        title: 'Documents',
        href: '/applicant/documents',
        icon: BookOpen,
    },
];

// Social Worker navigation items
const socialWorkerNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/social-worker/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Verifications',
        href: '/social-worker/verifications',
        icon: CheckCheck,
    },
    {
        title: 'Applications',
        href: '/social-worker/applications',
        icon: FileCheck,
    },
    {
        title: 'Interviews',
        href: '/social-worker/interviews',
        icon: CalendarDays,
    },
    {
        title: 'Clients',
        href: '/social-worker/clients',
        icon: Users,
    },
    {
        title: 'Reports',
        href: '/social-worker/reports',
        icon: BarChart,
    },
];

// MSWDO Officer navigation items
const mswdoOfficerNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/mswdo-officer/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'System Configuration',
        href: '/mswdo-officer/configuration',
        icon: Settings,
    },
    {
        title: 'Service Management',
        href: '/mswdo-officer/services',
        icon: Box,
    },
    {
        title: 'User Management',
        href: '/mswdo-officer/users',
        icon: Users,
    },
    {
        title: 'Budget Allocation',
        href: '/mswdo-officer/budget',
        icon: CreditCard,
    },
    {
        title: 'Database Management',
        href: '/mswdo-officer/database',
        icon: Database,
    },
    {
        title: 'Reports & Analytics',
        href: '/mswdo-officer/reports',
        icon: BarChart,
    },
];

const footerNavItems: NavItem[] = [
    
];

function normalizeRole(role: string): string {
    if (!role) return 'applicant';

    const roleMap: Record<string, string> = {
        'superadmin': 'mswdo-officer',
        'admin': 'mswdo-officer',
        'mswdo-officer': 'mswdo-officer',
        'MSWDO Officer': 'mswdo-officer',
        'social-worker': 'social-worker',
        'Social Worker': 'social-worker',
        'applicant': 'applicant',
        'client': 'applicant',
        'Applicant': 'applicant',
    };

    return roleMap[role.trim()] || 'applicant';
}

export function AppSidebar() {
    const { auth } = usePage().props as PageProps;
    const userRole = auth?.user?.role || 'applicant';
    const normalizedRole = normalizeRole(userRole);

    // Debug logging
    console.log('AppSidebar Debug:', {
        userRole,
        normalizedRole,
        authObject: auth,
        userObject: auth?.user
    });

    const getNavItems = () => {
        // Check if we're on an MSWDO Officer page - if so, force MSWDO nav items
        const currentPath = window.location.pathname;
        const isMswdoPage = currentPath.startsWith('/mswdo-officer/');
        const isSocialWorkerPage = currentPath.startsWith('/social-worker/');
        const isApplicantPage = currentPath.startsWith('/applicant/');

        let items;
        if (isMswdoPage) {
            console.log('Force showing MSWDO nav items due to URL path');
            items = mswdoOfficerNavItems;
        } else if (isSocialWorkerPage) {
            console.log('Force showing Social Worker nav items due to URL path');
            items = socialWorkerNavItems;
        } else if (isApplicantPage) {
            console.log('Force showing Applicant nav items due to URL path');
            items = applicantNavItems;
        } else {
            // Fallback to role-based logic
            switch (normalizedRole) {
                case 'social-worker':
                    items = socialWorkerNavItems;
                    break;
                case 'mswdo-officer':
                    items = mswdoOfficerNavItems;
                    break;
                default:
                    items = applicantNavItems;
            }
        }

        console.log('Selected nav items for role', normalizedRole, 'on path', currentPath, ':', items);
        return items;
    };

    const getDashboardRoute = () => {
        switch (normalizedRole) {
            case 'mswdo-officer':
                return '/mswdo-officer/dashboard';
            case 'social-worker':
                return '/social-worker/dashboard';
            default:
                return '/applicant/dashboard';
        }
    };

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href={getDashboardRoute()} prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={getNavItems()} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
