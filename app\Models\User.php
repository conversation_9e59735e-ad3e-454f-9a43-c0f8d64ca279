<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'status'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function profile()
    {
        return $this->hasOne(Profile::class);
    }

    public function adminProfile()
    {
        return $this->hasOne(AdminProfile::class);
    }

    public function isSocialWorker()
    {
        return $this->role === 'social-worker';
    }

    public function isMswdoOfficer()
    {
        return $this->role === 'mswdo-officer';
    }

    public function isApplicant()
    {
        return $this->role === 'applicant';
    }

    public function isVerified()
    {
        return $this->status === 'verified';
    }

    public function hasRole(string $role): bool
    {
        if ($role === 'social-worker') {
            return $this->role === 'social-worker' || $this->role === 'mswdo-officer';
        }
        return $this->role === $role;
    }
}
