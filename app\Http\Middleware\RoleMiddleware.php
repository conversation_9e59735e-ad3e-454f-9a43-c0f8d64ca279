<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $role): Response
    {
        if (!$request->user()) {
            return redirect()->route('login');
        }

        // For development: Allow access if user has no role set
        if ($request->user()->role === null) {
            return $next($request);
        }



        if ($request->user()->hasRole($role)) {
            return $next($request);
        }

        // If user doesn't have the required role, redirect to their appropriate dashboard
        switch ($request->user()->role) {
            case 'mswdo-officer':
                return redirect()->route('mswdo-officer.dashboard');
            case 'social-worker':
                return redirect()->route('social-worker.dashboard');
            case 'applicant':
                return redirect()->route('applicant.dashboard');
            default:
                return redirect()->route('login');
        }
    }
} 