import React from 'react';
import { Link } from '@inertiajs/react';
import { cn } from '@/lib/utils';
import AppLogo from '@/components/app-logo';
import { ArrowRightIcon } from 'lucide-react';
import HamburgerMenu from './hamburger-menu';

interface LandingNavProps {
    currentPath: string;
    auth: any;
}

function getDashboardRoute(userRole: string): string {
    switch (userRole) {
        case 'mswdo-officer':
            return route('mswdo-officer.dashboard');
        case 'social-worker':
            return route('social-worker.dashboard');
        default:
            return route('applicant.dashboard');
    }
}

export default function LandingNav({ currentPath, auth }: LandingNavProps) {
    const navItems = [
        { name: 'Home', href: route('home') },
        { name: 'Services', href: route('services-info') },
        { name: 'FAQs', href: route('faqs') },
        { name: 'Contact', href: route('contact') }
    ];

    return (
        <header className="container mx-auto px-4 py-6 flex justify-between items-center">
            <AppLogo />

            <nav className="flex items-center gap-4 ">
                {navItems.map((item) => (
                    <Link
                        key={item.name}
                        href={item.href}
                        className={cn(
                            "text-gray-700 hover:text-purple-700 font-medium relative pb-1",
                            "after:content-[''] after:absolute after:left-0 after:bottom-0 after:h-0.5 after:w-full after:bg-purple-600 hidden sm:inline-block",
                            currentPath === item.href
                                ? "after:scale-x-100"
                                : "after:scale-x-0 hover:after:scale-x-100 after:transition-transform" 
                        )}
                    >
                        {item.name}
                    </Link>
                ))}
                
                
                <div className="ml-4 border-l border-gray-200 pl-4 ">
                    {auth.user ? (
                        <Link
                            href={getDashboardRoute(auth.user.role)}
                            className="inline-flex items-center gap-2 rounded-md bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700 transition-colors"
                        >
                            Dashboard
                            <ArrowRightIcon className="h-4 w-4" />
                        </Link>
                    ) : (
                        <>
                            <Link
                                href={route('login')}
                                className="text-gray-700 hover:text-purple-700 font-medium"
                            >
                                Log in
                            </Link>
                            <Link
                                href={route('register')}
                                className="ml-4 rounded-md bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700 transition-colors"
                            >
                                Register
                            </Link>
                        </>
                    )}
                </div>

                <HamburgerMenu currentPath={currentPath} auth={auth} />

            </nav>
        </header>
    );
} 